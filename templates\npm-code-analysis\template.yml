spec:
  inputs:
    nodejs-image-name:
      default: registry.gitlab.dachser.com/dachser/shared-and-technology-services/development-products/development-platforms/gitlab/gitlab-runner-images/nodejs:22-latest
    scan-stage:
      default: post-test
      description: "Stage for running code analysis"
    log-level:
      default: "DEBUG"
    codeql-image-name:
      default: bint-docker-dfe.docker-artifactory.dach041.dachser.com/ci/codeql-analyzer:1.0.0
      description: "Image to use for CodeQL analysis"

---

# Hidden job template that other jobs can extend
.npm-setup:
  before_script:
    - mkdir -p reports
    - npm ci --ignore-engines
  variables:
    LOG_LEVEL: $[[ inputs.log-level ]]

eslint-code-analysis:
  stage: $[[ inputs.scan-stage ]]
  image: $[[ inputs.nodejs-image-name ]]
  extends: .npm-setup
  rules:
    - if: '$CI_COMMIT_TAG'
      when: never
    - when: always
  script:
    - echo "Excluding generated coverage files from analysis..."
    - touch .eslintignore || true
    - echo -e "coverage/**\ncoverage\n**/coverage/**" >> .eslintignore
    - npm run lint:report  # This runs: eslint . -f json > reports/eslint-report.json || true
    - |
      cat > convert-report.js <<'EOF'
      import fs from "fs";
      
      // Ensure reports directory exists
      if (!fs.existsSync("reports")) {
        fs.mkdirSync("reports", { recursive: true });
      }
      
      try {
        // Read the ESLint JSON report
        const eslintOutput = fs.existsSync("reports/eslint-report.json")
          ? fs.readFileSync("reports/eslint-report.json", "utf8")
          : "[]";
        const eslintData = JSON.parse(eslintOutput);
        const gitlabData = [];
        
        eslintData.forEach((file) => {
          file.messages.forEach((msg) => {
            const normalizedPath = file.filePath.replace(/^\/builds\/.*?\/.*?\//, "");
            gitlabData.push({
              description: `${msg.ruleId}: ${msg.message}`,
              fingerprint: `${normalizedPath}-${msg.line}-${msg.column}-${msg.ruleId}`,
              severity: msg.severity === 2 ? "critical" : "major",
              location: {
                path: normalizedPath,
                lines: {
                  begin: msg.line,
                },
              },
            });
          });
        });
        
        // Write the GitLab code quality format
        fs.writeFileSync("reports/gl-code-quality-report.json", JSON.stringify(gitlabData, null, 2));
        console.log("Successfully converted ESLint report to GitLab Code Quality format");
      } catch (error) {
        console.error("Error converting report:", error);
        // Ensure we create a valid JSON file even on error
        fs.writeFileSync("reports/gl-code-quality-report.json", "[]");
      }
      EOF
    - node convert-report.js
    - mv reports/gl-code-quality-report.json reports/gl-code-quality-eslint-report.json || echo "[]" > reports/gl-code-quality-eslint-report.json
    - test -f reports/gl-code-quality-eslint-report.json || echo "[]" > reports/gl-code-quality-eslint-report.json
  artifacts:
    reports:
      codequality: reports/gl-code-quality-eslint-report.json
    paths:
      - reports/gl-code-quality-eslint-report.json
  allow_failure: true
  
codeql-code-analysis:
  stage: $[[ inputs.scan-stage ]]
  image: $[[ inputs.codeql-image-name ]]
  extends: .npm-setup
  rules:
    - if: '$CI_COMMIT_TAG'
      when: never
    - when: always
  script:
    - |
      cat > .codeqlignore <<'EOF'
      coverage/**
      **/coverage/**
      node_modules/**
      **/node_modules/**
      dist/**
      build/**
      EOF

    - echo "Creating CodeQL database for JavaScript analysis..."
    - echo "[]" > reports/gl-code-quality-codeql-report.json
    - codeql database create codeql-db --language=javascript
    - codeql database analyze codeql-db javascript-security-and-quality --format=sarifv2.1.0 --output=results.sarif || true
    - |
      if [ -f results.sarif ] && [ -s results.sarif ]; then
        jq '[
            .runs[].results[]
            # Exclude coverage files, test helpers, and other generated files
            | select(.locations[0].physicalLocation.artifactLocation.uri | 
              test("(coverage/|tests/helper/|node_modules/|dist/|build/)") | not)
            # Also exclude specific problematic files by name
            | select(.locations[0].physicalLocation.artifactLocation.uri | 
              test("(prettify\\.js|sorter\\.js|prepareTests\\.js)$") | not)
            | {
                description: .message.text,
                fingerprint:
                  (
                    .id + ":" 
                    + (.locations[0].physicalLocation.artifactLocation.uri // "") + ":" 
                    + ((.locations[0].physicalLocation.region.startLine|tostring) // "0")
                  ),
                severity:
                  (if (.properties.tags // []) | contains(["security"]) then "critical" else "major" end),
                location: {
                  path: .locations[0].physicalLocation.artifactLocation.uri,
                  lines: { begin: .locations[0].physicalLocation.region.startLine }
                }
              }
          ]' results.sarif > reports/gl-code-quality-codeql-report.json || echo "Failed to convert SARIF to GitLab format"
      fi
  artifacts:
    reports:
      codequality: reports/gl-code-quality-codeql-report.json
    paths:
      - results.sarif
      - reports/gl-code-quality-codeql-report.json
  allow_failure: true
